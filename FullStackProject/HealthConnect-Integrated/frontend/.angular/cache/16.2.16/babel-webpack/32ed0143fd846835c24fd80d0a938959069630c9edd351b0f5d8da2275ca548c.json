{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  XHRLocalObject = require('./transport/sender/xhr-local'),\n  InfoAjax = require('./info-ajax');\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function (info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\ninherits(InfoReceiverIframe, EventEmitter);\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\nInfoReceiverIframe.prototype.close = function () {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\nmodule.exports = InfoReceiverIframe;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}