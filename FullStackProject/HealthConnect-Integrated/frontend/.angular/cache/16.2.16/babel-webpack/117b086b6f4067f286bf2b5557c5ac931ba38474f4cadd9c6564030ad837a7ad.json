{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  utils = require('./utils/event'),\n  IframeTransport = require('./transport/iframe'),\n  InfoReceiverIframe = require('./info-iframe-receiver');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n  var go = function () {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n    ifr.once('message', function (msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n        var info = d[0],\n          rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n    ifr.once('close', function () {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\ninherits(InfoIframe, EventEmitter);\nInfoIframe.enabled = function () {\n  return IframeTransport.enabled();\n};\nInfoIframe.prototype.close = function () {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\nmodule.exports = InfoIframe;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}