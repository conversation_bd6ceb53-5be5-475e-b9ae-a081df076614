{"ast": null, "code": "/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {}\n//# sourceMappingURL=stomp-headers.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}