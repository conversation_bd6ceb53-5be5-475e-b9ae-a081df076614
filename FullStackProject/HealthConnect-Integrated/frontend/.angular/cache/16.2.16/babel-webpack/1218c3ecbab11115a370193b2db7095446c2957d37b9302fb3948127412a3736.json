{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { UserService } from './services/user.service';\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nimport * as i0 from \"@angular/core\";\nexport let CoreModule = /*#__PURE__*/(() => {\n  class CoreModule {\n    constructor(parentModule) {\n      if (parentModule) {\n        throw new Error('CoreModule is already loaded. Import it in the AppModule only');\n      }\n    }\n    static {\n      this.ɵfac = function CoreModule_Factory(t) {\n        return new (t || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CoreModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [AuthService, UserService, AuthGuard, {\n          provide: HTTP_INTERCEPTORS,\n          useClass: AuthInterceptor,\n          multi: true\n        }],\n        imports: [CommonModule]\n      });\n    }\n  }\n  return CoreModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}