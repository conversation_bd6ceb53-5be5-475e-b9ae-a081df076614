{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../shared/shared.module';\nimport { ChatModule } from '../chat/chat.module';\nimport { PatientDashboardComponent } from './dashboard/dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  component: PatientDashboardComponent\n}];\nexport let PatientModule = /*#__PURE__*/(() => {\n  class PatientModule {\n    static {\n      this.ɵfac = function PatientModule_Factory(t) {\n        return new (t || PatientModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: PatientModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [SharedModule, ChatModule, RouterModule.forChild(routes)]\n      });\n    }\n  }\n  return PatientModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}