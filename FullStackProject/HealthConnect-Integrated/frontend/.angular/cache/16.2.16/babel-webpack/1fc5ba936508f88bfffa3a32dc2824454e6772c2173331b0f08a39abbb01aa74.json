{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction RegisterComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.successMessage, \" \");\n  }\n}\nfunction RegisterComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nfunction RegisterComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction RegisterComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFieldError(\"email\"), \" \");\n  }\n}\nfunction RegisterComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFieldError(\"password\"), \" \");\n  }\n}\nfunction RegisterComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getFieldError(\"confirmPassword\"), \" \");\n  }\n}\nfunction RegisterComponent_div_51_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"specialization\"), \" \");\n  }\n}\nfunction RegisterComponent_div_51_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"licenseNumber\"), \" \");\n  }\n}\nfunction RegisterComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"h6\", 53);\n    i0.ɵɵelement(2, \"i\", 22);\n    i0.ɵɵtext(3, \"Doctor Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"div\", 29)(6, \"label\", 54);\n    i0.ɵɵtext(7, \"Specialization *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 55);\n    i0.ɵɵtemplate(9, RegisterComponent_div_51_div_9_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"label\", 56);\n    i0.ɵɵtext(12, \"License Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 57);\n    i0.ɵɵtemplate(14, RegisterComponent_div_51_div_14_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 29)(16, \"label\", 58);\n    i0.ɵɵtext(17, \"Hospital/Clinic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 29)(20, \"label\", 60);\n    i0.ɵɵtext(21, \"Years of Experience\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 61);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r6.isFieldInvalid(\"specialization\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isFieldInvalid(\"specialization\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r6.isFieldInvalid(\"licenseNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isFieldInvalid(\"licenseNumber\"));\n  }\n}\nfunction RegisterComponent_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 62);\n  }\n}\nfunction RegisterComponent_span_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Creating Account...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(formBuilder, authService, router) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.isLoading = false;\n      this.errorMessage = '';\n      this.successMessage = '';\n      this.selectedRole = 'PATIENT';\n    }\n    ngOnInit() {\n      // Redirect if already logged in\n      if (this.authService.isAuthenticated()) {\n        this.redirectToDashboard();\n        return;\n      }\n      this.initializeForm();\n    }\n    initializeForm() {\n      this.registerForm = this.formBuilder.group({\n        fullName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]],\n        role: ['PATIENT', [Validators.required]],\n        phoneNumber: [''],\n        address: [''],\n        // Doctor-specific fields\n        specialization: [''],\n        licenseNumber: [''],\n        affiliation: [''],\n        yearsOfExperience: ['']\n      }, {\n        validators: this.passwordMatchValidator\n      });\n      // Watch for role changes\n      this.registerForm.get('role')?.valueChanges.subscribe(role => {\n        this.selectedRole = role;\n        this.updateValidators();\n      });\n    }\n    passwordMatchValidator(form) {\n      const password = form.get('password');\n      const confirmPassword = form.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n      } else {\n        if (confirmPassword?.errors?.['passwordMismatch']) {\n          delete confirmPassword.errors['passwordMismatch'];\n          if (Object.keys(confirmPassword.errors).length === 0) {\n            confirmPassword.setErrors(null);\n          }\n        }\n      }\n      return null;\n    }\n    updateValidators() {\n      const specialization = this.registerForm.get('specialization');\n      const licenseNumber = this.registerForm.get('licenseNumber');\n      if (this.selectedRole === 'DOCTOR') {\n        specialization?.setValidators([Validators.required]);\n        licenseNumber?.setValidators([Validators.required]);\n      } else {\n        specialization?.clearValidators();\n        licenseNumber?.clearValidators();\n      }\n      specialization?.updateValueAndValidity();\n      licenseNumber?.updateValueAndValidity();\n    }\n    onSubmit() {\n      if (this.registerForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n      const registerRequest = this.registerForm.value;\n      this.authService.register(registerRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          this.successMessage = 'Registration successful! Redirecting to dashboard...';\n          // Redirect after a short delay\n          setTimeout(() => {\n            this.redirectToDashboard();\n          }, 2000);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Registration failed. Please try again.';\n        }\n      });\n    }\n    redirectToDashboard() {\n      const user = this.authService.getCurrentUser();\n      if (user) {\n        if (user.role === 'DOCTOR') {\n          this.router.navigate(['/doctor/dashboard']);\n        } else if (user.role === 'PATIENT') {\n          this.router.navigate(['/patient/dashboard']);\n        } else {\n          this.router.navigate(['/']);\n        }\n      }\n    }\n    markFormGroupTouched() {\n      Object.keys(this.registerForm.controls).forEach(key => {\n        const control = this.registerForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.registerForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldError(fieldName) {\n      const field = this.registerForm.get(fieldName);\n      if (field?.errors) {\n        if (field.errors['required']) {\n          return `${this.getFieldDisplayName(fieldName)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n        if (field.errors['maxlength']) {\n          return `${this.getFieldDisplayName(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n        }\n        if (field.errors['passwordMismatch']) {\n          return 'Passwords do not match';\n        }\n      }\n      return '';\n    }\n    getFieldDisplayName(fieldName) {\n      const displayNames = {\n        fullName: 'Full Name',\n        email: 'Email',\n        password: 'Password',\n        confirmPassword: 'Confirm Password',\n        phoneNumber: 'Phone Number',\n        specialization: 'Specialization',\n        licenseNumber: 'License Number',\n        affiliation: 'Affiliation',\n        yearsOfExperience: 'Years of Experience'\n      };\n      return displayNames[fieldName] || fieldName;\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(t) {\n        return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        decls: 74,\n        vars: 20,\n        consts: [[1, \"container-fluid\", \"min-vh-100\", \"py-4\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"col-lg-6\"], [1, \"card\", \"shadow\"], [1, \"card-body\", \"p-4\"], [1, \"text-center\", \"mb-4\"], [1, \"bi\", \"bi-heart-pulse\", \"text-primary\", \"display-4\", \"mb-3\"], [1, \"fw-bold\", \"text-dark\"], [1, \"text-muted\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [1, \"form-label\", \"fw-semibold\"], [1, \"row\"], [1, \"col-6\"], [1, \"form-check\"], [\"type\", \"radio\", \"name\", \"role\", \"id\", \"rolePatient\", \"value\", \"PATIENT\", \"formControlName\", \"role\", 1, \"form-check-input\"], [\"for\", \"rolePatient\", 1, \"form-check-label\"], [1, \"bi\", \"bi-person\", \"me-2\"], [\"type\", \"radio\", \"name\", \"role\", \"id\", \"roleDoctor\", \"value\", \"DOCTOR\", \"formControlName\", \"role\", 1, \"form-check-input\"], [\"for\", \"roleDoctor\", 1, \"form-check-label\"], [1, \"bi\", \"bi-person-badge\", \"me-2\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"fullName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"form-control\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Create a password\", 1, \"form-control\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", 1, \"form-control\"], [\"class\", \"border-top pt-3 mb-3\", 4, \"ngIf\"], [1, \"border-top\", \"pt-3\", \"mb-4\"], [1, \"fw-semibold\", \"mb-3\"], [1, \"bi\", \"bi-telephone\", \"me-2\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Your phone number\", 1, \"form-control\"], [\"for\", \"address\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"address\", \"formControlName\", \"address\", \"placeholder\", \"Your address\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"py-2\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/auth/login\", 1, \"text-primary\", \"text-decoration-none\", \"fw-semibold\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"invalid-feedback\"], [1, \"border-top\", \"pt-3\", \"mb-3\"], [1, \"fw-semibold\", \"text-primary\", \"mb-3\"], [\"for\", \"specialization\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"specialization\", \"formControlName\", \"specialization\", \"placeholder\", \"e.g., Cardiology\", 1, \"form-control\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Medical license number\", 1, \"form-control\"], [\"for\", \"affiliation\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"affiliation\", \"formControlName\", \"affiliation\", \"placeholder\", \"Your workplace\", 1, \"form-control\"], [\"for\", \"yearsOfExperience\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"yearsOfExperience\", \"formControlName\", \"yearsOfExperience\", \"placeholder\", \"0\", \"min\", \"0\", \"max\", \"50\", 1, \"form-control\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"i\", 6);\n            i0.ɵɵelementStart(7, \"h2\", 7);\n            i0.ɵɵtext(8, \"Join HealthConnect\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p\", 8);\n            i0.ɵɵtext(10, \"Create your account to get started\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(11, RegisterComponent_div_11_Template, 3, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, RegisterComponent_div_12_Template, 3, 1, \"div\", 10);\n            i0.ɵɵelementStart(13, \"form\", 11);\n            i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_13_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n            i0.ɵɵtext(16, \"I am a:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 14)(18, \"div\", 15)(19, \"div\", 16);\n            i0.ɵɵelement(20, \"input\", 17);\n            i0.ɵɵelementStart(21, \"label\", 18);\n            i0.ɵɵelement(22, \"i\", 19);\n            i0.ɵɵtext(23, \"Patient \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"div\", 15)(25, \"div\", 16);\n            i0.ɵɵelement(26, \"input\", 20);\n            i0.ɵɵelementStart(27, \"label\", 21);\n            i0.ɵɵelement(28, \"i\", 22);\n            i0.ɵɵtext(29, \"Doctor \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(30, \"div\", 14)(31, \"div\", 23)(32, \"label\", 24);\n            i0.ɵɵtext(33, \"Full Name *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(34, \"input\", 25);\n            i0.ɵɵtemplate(35, RegisterComponent_div_35_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\", 23)(37, \"label\", 27);\n            i0.ɵɵtext(38, \"Email Address *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(39, \"input\", 28);\n            i0.ɵɵtemplate(40, RegisterComponent_div_40_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 29)(42, \"label\", 30);\n            i0.ɵɵtext(43, \"Password *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 31);\n            i0.ɵɵtemplate(45, RegisterComponent_div_45_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\", 29)(47, \"label\", 32);\n            i0.ɵɵtext(48, \"Confirm Password *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(49, \"input\", 33);\n            i0.ɵɵtemplate(50, RegisterComponent_div_50_Template, 2, 1, \"div\", 26);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(51, RegisterComponent_div_51_Template, 23, 6, \"div\", 34);\n            i0.ɵɵelementStart(52, \"div\", 35)(53, \"h6\", 36);\n            i0.ɵɵelement(54, \"i\", 37);\n            i0.ɵɵtext(55, \"Contact Information (Optional) \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"div\", 14)(57, \"div\", 29)(58, \"label\", 38);\n            i0.ɵɵtext(59, \"Phone Number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(60, \"input\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 29)(62, \"label\", 40);\n            i0.ɵɵtext(63, \"Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(64, \"input\", 41);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(65, \"button\", 42);\n            i0.ɵɵtemplate(66, RegisterComponent_span_66_Template, 1, 0, \"span\", 43);\n            i0.ɵɵtemplate(67, RegisterComponent_span_67_Template, 2, 0, \"span\", 44);\n            i0.ɵɵtemplate(68, RegisterComponent_span_68_Template, 2, 0, \"span\", 44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"div\", 45)(70, \"p\", 8);\n            i0.ɵɵtext(71, \" Already have an account? \");\n            i0.ɵɵelementStart(72, \"a\", 46);\n            i0.ɵɵtext(73, \" Sign in here \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n            i0.ɵɵadvance(21);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"fullName\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"fullName\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"email\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"email\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"password\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"password\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"confirmPassword\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"confirmPassword\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedRole === \"DOCTOR\");\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\".min-vh-100[_ngcontent-%COMP%]{min-height:100vh}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 4px 6px #0000001a}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}.form-check-label[_ngcontent-%COMP%]{cursor:pointer;font-weight:500}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#0d6efd 0%,#0b5ed7 100%);border:none;font-weight:500;border-radius:8px}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0b5ed7 0%,#0a58ca 100%);transform:translateY(-1px);box-shadow:0 4px 8px #0d6efd4d}.btn-primary[_ngcontent-%COMP%]:disabled{background:#6c757d;transform:none;box-shadow:none}.border-top[_ngcontent-%COMP%]{border-color:#e9ecef!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.alert[_ngcontent-%COMP%]{border:none;border-radius:8px}.form-control[_ngcontent-%COMP%]{border-radius:6px;border:1px solid #ced4da}.form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.card-body[_ngcontent-%COMP%]{padding:2rem 1.5rem!important}}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}