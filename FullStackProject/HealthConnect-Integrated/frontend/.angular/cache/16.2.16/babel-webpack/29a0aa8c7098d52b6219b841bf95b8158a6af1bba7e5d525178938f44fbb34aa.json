{"ast": null, "code": "import { TickerStrategy } from './types.js';\nexport class Ticker {\n  constructor(_interval, _strategy = TickerStrategy.Interval, _debug) {\n    this._interval = _interval;\n    this._strategy = _strategy;\n    this._debug = _debug;\n    this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n  }\n  start(tick) {\n    this.stop();\n    if (this.shouldUseWorker()) {\n      this.runWorker(tick);\n    } else {\n      this.runInterval(tick);\n    }\n  }\n  stop() {\n    this.disposeWorker();\n    this.disposeInterval();\n  }\n  shouldUseWorker() {\n    return typeof Worker !== 'undefined' && this._strategy === TickerStrategy.Worker;\n  }\n  runWorker(tick) {\n    this._debug('Using runWorker for outgoing pings');\n    if (!this._worker) {\n      this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], {\n        type: 'text/javascript'\n      })));\n      this._worker.onmessage = message => tick(message.data);\n    }\n  }\n  runInterval(tick) {\n    this._debug('Using runInterval for outgoing pings');\n    if (!this._timer) {\n      const startTime = Date.now();\n      this._timer = setInterval(() => {\n        tick(Date.now() - startTime);\n      }, this._interval);\n    }\n  }\n  disposeWorker() {\n    if (this._worker) {\n      this._worker.terminate();\n      delete this._worker;\n      this._debug('Outgoing ping disposeWorker');\n    }\n  }\n  disposeInterval() {\n    if (this._timer) {\n      clearInterval(this._timer);\n      delete this._timer;\n      this._debug('Outgoing ping disposeInterval');\n    }\n  }\n}\n//# sourceMappingURL=ticker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}