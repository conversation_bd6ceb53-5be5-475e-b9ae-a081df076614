{"ast": null, "code": "'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\nEvent.prototype.initEvent = function (eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\nEvent.prototype.stopPropagation = function () {};\nEvent.prototype.preventDefault = function () {};\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\nmodule.exports = Event;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}