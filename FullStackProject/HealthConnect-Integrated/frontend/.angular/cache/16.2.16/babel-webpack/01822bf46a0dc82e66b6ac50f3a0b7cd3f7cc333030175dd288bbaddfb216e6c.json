{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/auth.service\";\nimport * as i2 from \"../../core/services/user.service\";\nimport * as i3 from \"../../core/services/appointment.service\";\nimport * as i4 from \"../../core/services/chat.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../chat/chat-list/chat-list.component\";\nimport * as i8 from \"../../chat/chat-window/chat-window.component\";\nconst _c0 = [\"chatWindow\"];\nfunction PatientDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Loading your dashboard...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 46)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"h6\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h4\", 58);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"small\", 59);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const metric_r12 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", metric_r12.icon, \" display-6 \", metric_r12.color, \" mb-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", metric_r12.value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(metric_r12.unit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r4.getStatusBadgeClass(metric_r12.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 10, metric_r12.status));\n  }\n}\nfunction PatientDashboardComponent_div_3_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 60);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_23_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const action_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.navigateTo(action_r13.route));\n    });\n    i0.ɵɵelementStart(2, \"div\", 56)(3, \"div\", 61);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h6\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 62);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const action_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(action_r13.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", action_r13.icon, \" text-white fs-4\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r13.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r13.description);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No upcoming appointments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_34_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.navigateTo(\"/appointments/book\"));\n    });\n    i0.ɵɵelement(5, \"i\", 66);\n    i0.ɵɵtext(6, \"Book Appointment \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_3_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"div\", 69)(3, \"div\", 70);\n    i0.ɵɵelement(4, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h6\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 72);\n    i0.ɵɵelement(9, \"i\", 73);\n    i0.ɵɵtext(10);\n    i0.ɵɵelement(11, \"i\", 74);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 75)(17, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_35_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const appointment_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.navigateTo(\"/appointments/\" + appointment_r18.id));\n    });\n    i0.ɵɵelement(18, \"i\", 76);\n    i0.ɵɵtext(19, \"View Details \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const appointment_r18 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", appointment_r18.type === \"VIDEO_CALL\" ? \"camera-video\" : \"geo-alt\", \" text-primary\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Dr. \", appointment_r18.doctor == null ? null : appointment_r18.doctor.fullName, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", appointment_r18.date, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", appointment_r18.startTime, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r7.getStatusBadgeClass(appointment_r18.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(15, 9, appointment_r18.status), \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No messages yet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_47_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.openChatModal());\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵtext(6, \"Start Conversation \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind3(2, 2, chat_r23.lastMessage.content, 0, 50), \"\", chat_r23.lastMessage.content.length > 50 ? \"...\" : \"\", \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.formatChatTime(chat_r23.lastMessage.createdAt), \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", chat_r23.unreadCount, \" \");\n  }\n}\nfunction PatientDashboardComponent_div_3_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 68)(2, \"div\", 69);\n    i0.ɵɵelement(3, \"img\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"h6\", 71);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, PatientDashboardComponent_div_3_div_48_p_7_Template, 3, 6, \"p\", 81);\n    i0.ɵɵtemplate(8, PatientDashboardComponent_div_3_div_48_small_8_Template, 2, 1, \"small\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 75);\n    i0.ɵɵtemplate(10, PatientDashboardComponent_div_3_div_48_span_10_Template, 2, 1, \"span\", 83);\n    i0.ɵɵelementStart(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_div_48_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r31);\n      const chat_r23 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.openChat(chat_r23));\n    });\n    i0.ɵɵelement(12, \"i\", 84);\n    i0.ɵɵtext(13, \"Open \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const chat_r23 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", chat_r23.doctor.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", chat_r23.doctor.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Dr. \", chat_r23.doctor.fullName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.lastMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.lastMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", chat_r23.unreadCount > 0);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_57_hr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 90);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 69)(2, \"div\", 87);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 88)(5, \"h6\", 71);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 72);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 59);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PatientDashboardComponent_div_3_div_57_hr_11_Template, 1, 0, \"hr\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const activity_r32 = ctx.$implicit;\n    const isLast_r33 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate2(\"bi bi-\", activity_r32.icon, \" \", activity_r32.color, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r32.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r32.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r32.time);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r33);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_65_hr_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"hr\", 90);\n  }\n}\nfunction PatientDashboardComponent_div_3_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 69)(2, \"div\", 92);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 88)(5, \"h6\", 71);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 93);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, PatientDashboardComponent_div_3_div_65_hr_9_Template, 1, 0, \"hr\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tip_r35 = ctx.$implicit;\n    const isLast_r36 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"bi bi-\", tip_r35.icon, \" text-primary\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tip_r35.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tip_r35.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !isLast_r36);\n  }\n}\nfunction PatientDashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25)(4, \"div\")(5, \"h1\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 27);\n    i0.ɵɵtext(8, \"Here's your health overview for today\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.refreshData());\n    });\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵtext(11, \"Refresh \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"div\", 24)(14, \"h5\", 30);\n    i0.ɵɵelement(15, \"i\", 31);\n    i0.ɵɵtext(16, \"Health Metrics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, PatientDashboardComponent_div_3_div_17_Template, 13, 12, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 23)(19, \"div\", 24)(20, \"h5\", 30);\n    i0.ɵɵelement(21, \"i\", 33);\n    i0.ɵɵtext(22, \"Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, PatientDashboardComponent_div_3_div_23_Template, 9, 7, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 23)(25, \"div\", 24)(26, \"div\", 34)(27, \"div\", 35)(28, \"h6\", 36);\n    i0.ɵɵelement(29, \"i\", 37);\n    i0.ɵɵtext(30, \"Upcoming Appointments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.navigateTo(\"/appointments\"));\n    });\n    i0.ɵɵtext(32, \" View All \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 39);\n    i0.ɵɵtemplate(34, PatientDashboardComponent_div_3_div_34_Template, 7, 0, \"div\", 40);\n    i0.ɵɵtemplate(35, PatientDashboardComponent_div_3_div_35_Template, 20, 11, \"div\", 41);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 34)(39, \"div\", 35)(40, \"h6\", 36);\n    i0.ɵɵelement(41, \"i\", 9);\n    i0.ɵɵtext(42, \"Recent Messages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PatientDashboardComponent_div_3_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.openChatModal());\n    });\n    i0.ɵɵelement(44, \"i\", 42);\n    i0.ɵɵtext(45, \"New Message \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 39);\n    i0.ɵɵtemplate(47, PatientDashboardComponent_div_3_div_47_Template, 7, 0, \"div\", 40);\n    i0.ɵɵtemplate(48, PatientDashboardComponent_div_3_div_48_Template, 14, 6, \"div\", 43);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 44)(50, \"div\", 45)(51, \"div\", 46)(52, \"div\", 47)(53, \"h6\", 36);\n    i0.ɵɵelement(54, \"i\", 48);\n    i0.ɵɵtext(55, \"Recent Activities \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 39);\n    i0.ɵɵtemplate(57, PatientDashboardComponent_div_3_div_57_Template, 12, 8, \"div\", 49);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 45)(59, \"div\", 46)(60, \"div\", 47)(61, \"h6\", 36);\n    i0.ɵɵelement(62, \"i\", 50);\n    i0.ɵɵtext(63, \"Health Tips \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 39);\n    i0.ɵɵtemplate(65, PatientDashboardComponent_div_3_div_65_Template, 10, 6, \"div\", 51);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(66, \"div\", 44)(67, \"div\", 24)(68, \"div\", 52);\n    i0.ɵɵelement(69, \"i\", 53);\n    i0.ɵɵelementStart(70, \"div\")(71, \"h6\", 54);\n    i0.ɵɵtext(72, \"Emergency Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"p\", 36);\n    i0.ɵɵtext(74, \"For medical emergencies, call \");\n    i0.ɵɵelementStart(75, \"strong\");\n    i0.ɵɵtext(76, \"911\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(77, \" or visit your nearest emergency room.\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", \", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName, \"!\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthMetrics);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.quickActions);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.upcomingAppointments.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.upcomingAppointments);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentChats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentChats);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentActivities);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.healthTips);\n  }\n}\nexport let PatientDashboardComponent = /*#__PURE__*/(() => {\n  class PatientDashboardComponent {\n    constructor(authService, userService, appointmentService, chatService, router) {\n      this.authService = authService;\n      this.userService = userService;\n      this.appointmentService = appointmentService;\n      this.chatService = chatService;\n      this.router = router;\n      this.currentUser = null;\n      this.isLoading = true;\n      this.error = '';\n      this.appointments = [];\n      this.upcomingAppointments = [];\n      this.recentChats = [];\n      this.healthMetrics = [{\n        name: 'Heart Rate',\n        value: '72',\n        unit: 'bpm',\n        status: 'normal',\n        icon: 'heart-pulse',\n        color: 'text-success'\n      }, {\n        name: 'Blood Pressure',\n        value: '120/80',\n        unit: 'mmHg',\n        status: 'normal',\n        icon: 'activity',\n        color: 'text-success'\n      }, {\n        name: 'Weight',\n        value: '70',\n        unit: 'kg',\n        status: 'normal',\n        icon: 'speedometer2',\n        color: 'text-info'\n      }, {\n        name: 'Temperature',\n        value: '98.6',\n        unit: '°F',\n        status: 'normal',\n        icon: 'thermometer-half',\n        color: 'text-success'\n      }];\n      this.quickActions = [{\n        title: 'Book Appointment',\n        description: 'Schedule a consultation with a doctor',\n        icon: 'calendar-plus',\n        color: 'bg-primary',\n        route: '/appointments/book'\n      }, {\n        title: 'Find Doctors',\n        description: 'Browse available healthcare providers',\n        icon: 'search',\n        color: 'bg-info',\n        route: '/appointments/doctors'\n      }, {\n        title: 'Health Assistant',\n        description: 'Get AI-powered health guidance',\n        icon: 'robot',\n        color: 'bg-success',\n        route: '/health-bot'\n      }, {\n        title: 'Messages',\n        description: 'Chat with your healthcare providers',\n        icon: 'chat-dots',\n        color: 'bg-warning',\n        route: '/chat'\n      }];\n      this.recentActivities = [{\n        title: 'Appointment Scheduled',\n        description: 'Consultation with Dr. Smith on Dec 15, 2024',\n        time: '2 hours ago',\n        icon: 'calendar-check',\n        color: 'text-primary'\n      }, {\n        title: 'Health Metrics Updated',\n        description: 'Blood pressure and weight recorded',\n        time: '1 day ago',\n        icon: 'graph-up',\n        color: 'text-success'\n      }, {\n        title: 'Message Received',\n        description: 'New message from Dr. Johnson',\n        time: '2 days ago',\n        icon: 'envelope',\n        color: 'text-info'\n      }];\n      this.healthTips = [{\n        title: 'Stay Hydrated',\n        description: 'Drink at least 8 glasses of water daily for optimal health.',\n        icon: 'droplet'\n      }, {\n        title: 'Regular Exercise',\n        description: 'Aim for 30 minutes of moderate exercise 5 days a week.',\n        icon: 'bicycle'\n      }, {\n        title: 'Healthy Sleep',\n        description: 'Get 7-9 hours of quality sleep each night.',\n        icon: 'moon'\n      }];\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadAppointments();\n      this.loadRecentChats();\n    }\n    loadUserData() {\n      this.authService.currentUser$.subscribe({\n        next: user => {\n          this.currentUser = user;\n          this.isLoading = false;\n        },\n        error: error => {\n          this.error = 'Failed to load user data';\n          this.isLoading = false;\n        }\n      });\n    }\n    loadAppointments() {\n      this.appointmentService.getPatientAppointments().subscribe({\n        next: appointments => {\n          this.appointments = appointments;\n          this.upcomingAppointments = appointments.filter(apt => new Date(apt.date) >= new Date()).slice(0, 3); // Show only next 3 appointments\n          this.updateRecentActivities();\n        },\n        error: error => {\n          console.error('Failed to load appointments:', error);\n        }\n      });\n    }\n    updateRecentActivities() {\n      // Update recent activities with real appointment data\n      const recentAppointments = this.appointments.filter(apt => apt.status === 'SCHEDULED' || apt.status === 'CONFIRMED').slice(0, 2);\n      this.recentActivities = [...recentAppointments.map(apt => ({\n        title: 'Appointment Scheduled',\n        description: `Consultation with Dr. ${apt.doctor?.fullName} on ${apt.date}`,\n        time: this.getTimeAgo(apt.createdAt),\n        icon: 'calendar-check',\n        color: 'text-primary'\n      })), ...this.recentActivities.slice(recentAppointments.length)];\n    }\n    getTimeAgo(date) {\n      const now = new Date();\n      const appointmentDate = new Date(date);\n      const diffInHours = Math.floor((now.getTime() - appointmentDate.getTime()) / (1000 * 60 * 60));\n      if (diffInHours < 1) return 'Just now';\n      if (diffInHours < 24) return `${diffInHours} hours ago`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      return `${diffInDays} days ago`;\n    }\n    navigateTo(route) {\n      this.router.navigate([route]);\n    }\n    getGreeting() {\n      const hour = new Date().getHours();\n      if (hour < 12) return 'Good morning';\n      if (hour < 18) return 'Good afternoon';\n      return 'Good evening';\n    }\n    getStatusBadgeClass(status) {\n      switch (status) {\n        case 'normal':\n          return 'badge bg-success';\n        case 'warning':\n          return 'badge bg-warning';\n        case 'danger':\n          return 'badge bg-danger';\n        default:\n          return 'badge bg-secondary';\n      }\n    }\n    refreshData() {\n      this.isLoading = true;\n      this.loadAppointments();\n      this.loadRecentChats();\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 1000);\n    }\n    loadRecentChats() {\n      this.chatService.getUserChats().subscribe({\n        next: chats => {\n          this.recentChats = chats.slice(0, 3); // Show only recent 3 chats\n        },\n\n        error: error => {\n          console.error('Failed to load chats:', error);\n        }\n      });\n    }\n    openChatModal() {\n      const modalElement = document.getElementById('chatModal');\n      if (modalElement) {\n        const modal = new window.bootstrap.Modal(modalElement);\n        modal.show();\n      }\n    }\n    openChat(chat) {\n      this.openChatModal();\n      // Wait for modal to open, then load chat\n      setTimeout(() => {\n        this.onChatSelected(chat);\n      }, 300);\n    }\n    onChatSelected(chat) {\n      if (this.chatWindow) {\n        this.chatWindow.loadChat(chat);\n      }\n    }\n    formatChatTime(dateString) {\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n      if (diffInHours < 1) {\n        return 'Just now';\n      } else if (diffInHours < 24) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      } else {\n        return date.toLocaleDateString();\n      }\n    }\n    static {\n      this.ɵfac = function PatientDashboardComponent_Factory(t) {\n        return new (t || PatientDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AppointmentService), i0.ɵɵdirectiveInject(i4.ChatService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PatientDashboardComponent,\n        selectors: [[\"app-patient-dashboard\"]],\n        viewQuery: function PatientDashboardComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatWindow = _t.first);\n          }\n        },\n        decls: 19,\n        vars: 3,\n        consts: [[1, \"container-fluid\", \"py-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"chatModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"chatModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"chatModalLabel\", 1, \"modal-title\"], [1, \"bi\", \"bi-chat-dots\", \"me-2\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\", \"p-0\", 2, \"height\", \"600px\"], [1, \"row\", \"h-100\", \"g-0\"], [1, \"col-md-4\", \"border-end\"], [3, \"chatSelected\"], [1, \"col-md-8\"], [\"chatWindow\", \"\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h3\", \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [1, \"mb-3\"], [1, \"bi\", \"bi-heart-pulse\", \"me-2\", \"text-primary\"], [\"class\", \"col-md-3 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightning\", \"me-2\", \"text-primary\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-calendar-check\", \"me-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"card-body\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-chat-plus\", \"me-1\"], [\"class\", \"chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\"], [1, \"col-md-6\", \"mb-4\"], [1, \"card\", \"h-100\"], [1, \"card-header\"], [1, \"bi\", \"bi-clock-history\", \"me-2\"], [\"class\", \"activity-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-lightbulb\", \"me-2\"], [\"class\", \"tip-item d-flex align-items-start mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-info\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-info-circle\", \"me-3\", \"fs-4\"], [1, \"alert-heading\", \"mb-1\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-3\"], [1, \"card-body\", \"text-center\"], [1, \"card-title\"], [1, \"mb-2\"], [1, \"text-muted\"], [1, \"card\", \"h-100\", \"action-card\", 3, \"click\"], [1, \"rounded-circle\", \"d-inline-flex\", \"align-items-center\", \"justify-content-center\", \"mb-3\", 2, \"width\", \"60px\", \"height\", \"60px\"], [1, \"card-text\", \"text-muted\", \"small\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-calendar-x\", \"display-6\", \"mb-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-calendar-plus\", \"me-2\"], [1, \"appointment-item\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-shrink-0\", \"me-3\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"45px\", \"height\", \"45px\"], [1, \"mb-1\"], [1, \"mb-1\", \"text-muted\", \"small\"], [1, \"bi\", \"bi-calendar\", \"me-1\"], [1, \"bi\", \"bi-clock\", \"ms-2\", \"me-1\"], [1, \"flex-shrink-0\"], [1, \"bi\", \"bi-eye\", \"me-1\"], [1, \"bi\", \"bi-chat-square-text\", \"display-6\", \"mb-3\"], [1, \"bi\", \"bi-chat-plus\", \"me-2\"], [1, \"chat-preview\", \"d-flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"mb-2\", \"rounded\", \"border\"], [1, \"rounded-circle\", 2, \"width\", \"45px\", \"height\", \"45px\", \"object-fit\", \"cover\", 3, \"src\", \"alt\"], [\"class\", \"mb-1 text-muted small\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"badge bg-primary rounded-pill me-2\", 4, \"ngIf\"], [1, \"bi\", \"bi-chat\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"me-2\"], [1, \"activity-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"rounded-circle\", \"bg-light\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"flex-grow-1\"], [\"class\", \"my-3\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"tip-item\", \"d-flex\", \"align-items-start\", \"mb-3\"], [1, \"rounded-circle\", \"bg-primary\", \"bg-opacity-10\", \"d-flex\", \"align-items-center\", \"justify-content-center\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"mb-0\", \"text-muted\", \"small\"]],\n        template: function PatientDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, PatientDashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n            i0.ɵɵtemplate(2, PatientDashboardComponent_div_2_Template, 3, 1, \"div\", 2);\n            i0.ɵɵtemplate(3, PatientDashboardComponent_div_3_Template, 78, 10, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h5\", 8);\n            i0.ɵɵelement(9, \"i\", 9);\n            i0.ɵɵtext(10, \"Messages \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"button\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"app-chat-list\", 14);\n            i0.ɵɵlistener(\"chatSelected\", function PatientDashboardComponent_Template_app_chat_list_chatSelected_15_listener($event) {\n              return ctx.onChatSelected($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 15);\n            i0.ɵɵelement(17, \"app-chat-window\", null, 16);\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i7.ChatListComponent, i8.ChatWindowComponent, i6.SlicePipe, i6.TitleCasePipe],\n        styles: [\".action-card[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease;border:none;box-shadow:0 2px 4px #0000001a}.action-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.activity-item[_ngcontent-%COMP%], .tip-item[_ngcontent-%COMP%]{border-bottom:1px solid #f8f9fa;padding-bottom:1rem}.activity-item[_ngcontent-%COMP%]:last-child, .tip-item[_ngcontent-%COMP%]:last-child{border-bottom:none;padding-bottom:0}.bg-primary[_ngcontent-%COMP%]{background-color:#0d6efd!important}.bg-info[_ngcontent-%COMP%]{background-color:#0dcaf0!important}.bg-success[_ngcontent-%COMP%]{background-color:#198754!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.display-6[_ngcontent-%COMP%]{font-size:2rem}}\"]\n      });\n    }\n  }\n  return PatientDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}