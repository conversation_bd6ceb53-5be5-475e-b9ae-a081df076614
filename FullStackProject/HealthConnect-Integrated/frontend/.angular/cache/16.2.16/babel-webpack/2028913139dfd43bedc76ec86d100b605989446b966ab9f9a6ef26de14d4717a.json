{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  objectUtils = require('./utils/object');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n  this.xo.once('finish', function (status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = +new Date() - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\ninherits(InfoAjax, EventEmitter);\nInfoAjax.prototype.close = function () {\n  this.removeAllListeners();\n  this.xo.close();\n};\nmodule.exports = InfoAjax;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}