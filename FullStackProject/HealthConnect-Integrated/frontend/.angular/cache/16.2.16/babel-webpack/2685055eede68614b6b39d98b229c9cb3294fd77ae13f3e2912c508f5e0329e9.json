{"ast": null, "code": "import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { FrameImpl } from './frame-impl.js';\nimport { Parser } from './parser.js';\nimport { Ticker } from './ticker.js';\nimport { StompSocketState } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  get connectedVersion() {\n    return this._connectedVersion;\n  }\n  get connected() {\n    return this._connected;\n  }\n  constructor(_client, _webSocket, config) {\n    this._client = _client;\n    this._webSocket = _webSocket;\n    this._connected = false;\n    this._serverFrameHandlers = {\n      // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n      CONNECTED: frame => {\n        this.debug(`connected to server ${frame.headers.server}`);\n        this._connected = true;\n        this._connectedVersion = frame.headers.version;\n        // STOMP version 1.2 needs header values to be escaped\n        if (this._connectedVersion === Versions.V1_2) {\n          this._escapeHeaderValues = true;\n        }\n        this._setupHeartbeat(frame.headers);\n        this.onConnect(frame);\n      },\n      // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n      MESSAGE: frame => {\n        // the callback is registered when the client calls\n        // `subscribe()`.\n        // If there is no registered subscription for the received message,\n        // the default `onUnhandledMessage` callback is used that the client can set.\n        // This is useful for subscriptions that are automatically created\n        // on the browser side (e.g. [RabbitMQ's temporary\n        // queues](https://www.rabbitmq.com/stomp.html)).\n        const subscription = frame.headers.subscription;\n        const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n        // bless the frame to be a Message\n        const message = frame;\n        const client = this;\n        const messageId = this._connectedVersion === Versions.V1_2 ? message.headers.ack : message.headers['message-id'];\n        // add `ack()` and `nack()` methods directly to the returned frame\n        // so that a simple call to `message.ack()` can acknowledge the message.\n        message.ack = (headers = {}) => {\n          return client.ack(messageId, subscription, headers);\n        };\n        message.nack = (headers = {}) => {\n          return client.nack(messageId, subscription, headers);\n        };\n        onReceive(message);\n      },\n      // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n      RECEIPT: frame => {\n        const callback = this._receiptWatchers[frame.headers['receipt-id']];\n        if (callback) {\n          callback(frame);\n          // Server will acknowledge only once, remove the callback\n          delete this._receiptWatchers[frame.headers['receipt-id']];\n        } else {\n          this.onUnhandledReceipt(frame);\n        }\n      },\n      // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n      ERROR: frame => {\n        this.onStompError(frame);\n      }\n    };\n    // used to index subscribers\n    this._counter = 0;\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n    this._partialData = '';\n    this._escapeHeaderValues = false;\n    this._lastServerActivityTS = Date.now();\n    this.debug = config.debug;\n    this.stompVersions = config.stompVersions;\n    this.connectHeaders = config.connectHeaders;\n    this.disconnectHeaders = config.disconnectHeaders;\n    this.heartbeatIncoming = config.heartbeatIncoming;\n    this.heartbeatOutgoing = config.heartbeatOutgoing;\n    this.splitLargeFrames = config.splitLargeFrames;\n    this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n    this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n    this.logRawCommunication = config.logRawCommunication;\n    this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n    this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n    this.onConnect = config.onConnect;\n    this.onDisconnect = config.onDisconnect;\n    this.onStompError = config.onStompError;\n    this.onWebSocketClose = config.onWebSocketClose;\n    this.onWebSocketError = config.onWebSocketError;\n    this.onUnhandledMessage = config.onUnhandledMessage;\n    this.onUnhandledReceipt = config.onUnhandledReceipt;\n    this.onUnhandledFrame = config.onUnhandledFrame;\n  }\n  start() {\n    const parser = new Parser(\n    // On Frame\n    rawFrame => {\n      const frame = FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n      // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n      if (!this.logRawCommunication) {\n        this.debug(`<<< ${frame}`);\n      }\n      const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n      serverFrameHandler(frame);\n    },\n    // On Incoming Ping\n    () => {\n      this.debug('<<< PONG');\n    });\n    this._webSocket.onmessage = evt => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n      if (this.logRawCommunication) {\n        const rawChunkAsString = evt.data instanceof ArrayBuffer ? new TextDecoder().decode(evt.data) : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n      parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n    };\n    this._webSocket.onclose = closeEvent => {\n      this.debug(`Connection closed to ${this._webSocket.url}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n    this._webSocket.onerror = errorEvent => {\n      this.onWebSocketError(errorEvent);\n    };\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = Object.assign({}, this.connectHeaders);\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [this.heartbeatOutgoing, this.heartbeatIncoming].join(',');\n      this._transmit({\n        command: 'CONNECT',\n        headers: connectHeaders\n      });\n    };\n  }\n  _setupHeartbeat(headers) {\n    if (headers.version !== Versions.V1_1 && headers.version !== Versions.V1_2) {\n      return;\n    }\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat'].split(',').map(v => parseInt(v, 10));\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n      this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n      this._pinger.start(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      });\n    }\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n  _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug('Discarding websocket, the underlying socket may linger for a while');\n      this.discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n  forceDisconnect() {\n    if (this._webSocket) {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n  _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n  discardWebsocket() {\n    if (typeof this._webSocket.terminate !== 'function') {\n      augmentWebsocket(this._webSocket, msg => this.debug(msg));\n    }\n    // @ts-ignore - this method will be there at this stage\n    this._webSocket.terminate();\n  }\n  _transmit(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader\n    });\n    let rawChunk = frame.serialize();\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n  dispose() {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({\n          command: 'DISCONNECT',\n          headers: disconnectHeaders\n        });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeWebsocket();\n      }\n    }\n  }\n  _cleanUp() {\n    this._connected = false;\n    if (this._pinger) {\n      this._pinger.stop();\n      this._pinger = undefined;\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n      this._ponger = undefined;\n    }\n  }\n  publish(params) {\n    const {\n      destination,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const hdrs = Object.assign({\n      destination\n    }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    });\n  }\n  watchForReceipt(receiptId, callback) {\n    this._receiptWatchers[receiptId] = callback;\n  }\n  subscribe(destination, callback, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({\n      command: 'SUBSCRIBE',\n      headers\n    });\n    const client = this;\n    return {\n      id: headers.id,\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      }\n    };\n  }\n  unsubscribe(id, headers = {}) {\n    headers = Object.assign({}, headers);\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({\n      command: 'UNSUBSCRIBE',\n      headers\n    });\n  }\n  begin(transactionId) {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId\n      }\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit() {\n        client.commit(txId);\n      },\n      abort() {\n        client.abort(txId);\n      }\n    };\n  }\n  commit(transactionId) {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  abort(transactionId) {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  ack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({\n      command: 'ACK',\n      headers\n    });\n  }\n  nack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({\n      command: 'NACK',\n      headers\n    });\n  }\n}\n//# sourceMappingURL=stomp-handler.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}