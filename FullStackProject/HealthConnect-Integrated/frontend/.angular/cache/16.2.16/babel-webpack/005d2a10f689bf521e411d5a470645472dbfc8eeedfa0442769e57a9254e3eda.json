{"ast": null, "code": "'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n  if (!port) return false;\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n      return port !== 80;\n    case 'https':\n    case 'wss':\n      return port !== 443;\n    case 'ftp':\n      return port !== 21;\n    case 'gopher':\n      return port !== 70;\n    case 'file':\n      return false;\n  }\n  return port !== 0;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}