{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  urlUtils = require('../../utils/url'),\n  BufferedSender = require('./buffered-sender'),\n  Polling = require('./polling');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function (msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function (code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\ninherits(SenderReceiver, BufferedSender);\nSenderReceiver.prototype.close = function () {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\nmodule.exports = SenderReceiver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}